//+------------------------------------------------------------------+
//|                                                   S.G_XAUUSD.mq5 |
//|                                RSI-EMA Momentum Hunter Strategy   |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "S.G Trading Robot"
#property link      ""
#property version   "1.00"
#property description "RSI-EMA Momentum Hunter - High Probability Trading EA"

//--- Include necessary files
#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include <Trade/SymbolInfo.mqh>

//--- Input parameters
input group "=== STRATEGY SETTINGS ==="
input double   RiskPercent = 1.5;           // Risk per trade (%)
input int      MaxConcurrentTrades = 3;     // Maximum concurrent positions
input double   ATR_SL_Multiplier = 2.0;     // ATR multiplier for Stop Loss
input double   TP1_Multiplier = 1.5;        // Take Profit 1 multiplier (1:1.5 RR)
input double   TP2_Multiplier = 2.5;        // Take Profit 2 multiplier (1:2.5 RR)

input group "=== INDICATOR SETTINGS ==="
input int      EMA_Fast_Period = 5;         // EMA Fast period
input int      EMA_Slow_Period = 12;        // EMA Slow period
input int      RSI_Period = 21;             // RSI period
input int      ATR_Period = 14;             // ATR period
input int      H4_EMA_Period = 21;          // H4 trend EMA period

input group "=== STOP LOSS LIMITS ==="
input int      MinStopLoss = 15;            // Minimum stop loss (pips)
input int      MaxStopLoss = 30;            // Maximum stop loss (pips)

input group "=== TIME FILTER ==="
input bool     UseTimeFilter = true;        // Enable time filter
input int      LondonStart = 8;             // London session start (GMT)
input int      LondonEnd = 16;              // London session end (GMT)
input int      NewYorkStart = 13;           // New York session start (GMT)
input int      NewYorkEnd = 21;             // New York session end (GMT)
input bool     AvoidFriday = true;          // Avoid Friday after 15:00 GMT
input int      FridayCloseHour = 15;        // Friday close hour (GMT)

//--- Global variables
int handleEMA_Fast, handleEMA_Slow, handleRSI, handleATR, handleH4_EMA;
datetime lastBarTime = 0;
bool emaFastAboveSlow = false;
bool prevEmaFastAboveSlow = false;

//--- Trade management
CTrade trade;
CPositionInfo position;
CSymbolInfo symbol;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize symbol
    if(!symbol.Name(_Symbol))
    {
        Print("Error initializing symbol: ", _Symbol);
        return INIT_FAILED;
    }

    // Initialize H1 indicators
    handleEMA_Fast = iMA(_Symbol, PERIOD_H1, EMA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
    handleEMA_Slow = iMA(_Symbol, PERIOD_H1, EMA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);
    handleRSI = iRSI(_Symbol, PERIOD_H1, RSI_Period, PRICE_CLOSE);
    handleATR = iATR(_Symbol, PERIOD_H1, ATR_Period);

    // Initialize H4 trend indicator
    handleH4_EMA = iMA(_Symbol, PERIOD_H4, H4_EMA_Period, 0, MODE_EMA, PRICE_CLOSE);

    // Check if all indicators are created successfully
    if(handleEMA_Fast == INVALID_HANDLE || handleEMA_Slow == INVALID_HANDLE ||
       handleRSI == INVALID_HANDLE || handleATR == INVALID_HANDLE || handleH4_EMA == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }

    Print("RSI-EMA Momentum Hunter EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(handleEMA_Fast);
    IndicatorRelease(handleEMA_Slow);
    IndicatorRelease(handleRSI);
    IndicatorRelease(handleATR);
    IndicatorRelease(handleH4_EMA);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new H1 bar
    datetime currentBarTime = iTime(_Symbol, PERIOD_H1, 0);

    if(currentBarTime == lastBarTime)
        return;

    lastBarTime = currentBarTime;

    // Check trading conditions
    if(!IsTimeToTrade())
        return;

    if(CountOpenPositions() >= MaxConcurrentTrades)
        return;

    // Get indicator values
    double emaFast[], emaSlow[], rsi[], atr[], h4Ema[];

    if(!GetIndicatorValues(emaFast, emaSlow, rsi, atr, h4Ema))
        return;

    // Update EMA cross status
    prevEmaFastAboveSlow = emaFastAboveSlow;
    emaFastAboveSlow = (emaFast[0] > emaSlow[0]);

    // Check for EMA cross and other conditions
    int signal = AnalyzeMarket(emaFast, emaSlow, rsi, atr, h4Ema);

    if(signal != 0)
    {
        ExecuteTrade(signal, atr[0]);
    }
}

//+------------------------------------------------------------------+
//| Get indicator values                                             |
//+------------------------------------------------------------------+
bool GetIndicatorValues(double &emaFast[], double &emaSlow[], double &rsi[],
                       double &atr[], double &h4Ema[])
{
    ArraySetAsSeries(emaFast, true);
    ArraySetAsSeries(emaSlow, true);
    ArraySetAsSeries(rsi, true);
    ArraySetAsSeries(atr, true);
    ArraySetAsSeries(h4Ema, true);

    if(CopyBuffer(handleEMA_Fast, 0, 0, 3, emaFast) < 3 ||
       CopyBuffer(handleEMA_Slow, 0, 0, 3, emaSlow) < 3 ||
       CopyBuffer(handleRSI, 0, 0, 3, rsi) < 3 ||
       CopyBuffer(handleATR, 0, 0, 3, atr) < 3 ||
       CopyBuffer(handleH4_EMA, 0, 0, 2, h4Ema) < 2)
    {
        Print("Error copying indicator buffers");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Count open positions                                             |
//+------------------------------------------------------------------+
int CountOpenPositions()
{
    int count = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == _Symbol)
                count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Analyze market conditions                                        |
//+------------------------------------------------------------------+
int AnalyzeMarket(const double &emaFast[], const double &emaSlow[],
                 const double &rsi[], const double &atr[], const double &h4Ema[])
{
    double currentPrice = symbol.Bid();

    // Check for EMA cross
    bool bullishCross = !prevEmaFastAboveSlow && emaFastAboveSlow;
    bool bearishCross = prevEmaFastAboveSlow && !emaFastAboveSlow;

    // LONG signal conditions
    if(bullishCross && rsi[0] > 50 && currentPrice > h4Ema[0])
    {
        return 1; // BUY signal
    }

    // SHORT signal conditions
    if(bearishCross && rsi[0] < 50 && currentPrice < h4Ema[0])
    {
        return -1; // SELL signal
    }

    return 0; // No signal
}

//+------------------------------------------------------------------+
//| Normalize lot size                                               |
//+------------------------------------------------------------------+
double NormalizeLot(double lotSize)
{
    double minLot = symbol.LotsMin();
    double maxLot = symbol.LotsMax();
    double lotStep = symbol.LotsStep();

    lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));

    return lotSize;
}

//+------------------------------------------------------------------+
//| Execute trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(int signal, double currentATR)
{
    double lotSize = CalculateLotSize(currentATR);
    double sl, tp1, tp2;

    if(signal == 1) // BUY
    {
        double price = symbol.Ask();
        sl = CalculateStopLoss(price, currentATR, true);
        tp1 = price + ((price - sl) * TP1_Multiplier);
        tp2 = price + ((price - sl) * TP2_Multiplier);

        // Split position into two parts for partial profit taking
        double lotSize1 = NormalizeLot(lotSize * 0.5);
        double lotSize2 = NormalizeLot(lotSize * 0.5);

        if(trade.Buy(lotSize1, _Symbol, price, sl, tp1, "RSI-EMA BUY TP1"))
        {
            Print("BUY order 1 executed at ", price, " SL: ", sl, " TP1: ", tp1);

            if(trade.Buy(lotSize2, _Symbol, price, sl, tp2, "RSI-EMA BUY TP2"))
            {
                Print("BUY order 2 executed at ", price, " SL: ", sl, " TP2: ", tp2);
            }
        }
    }
    else if(signal == -1) // SELL
    {
        double price = symbol.Bid();
        sl = CalculateStopLoss(price, currentATR, false);
        tp1 = price - ((sl - price) * TP1_Multiplier);
        tp2 = price - ((sl - price) * TP2_Multiplier);

        // Split position into two parts for partial profit taking
        double lotSize1 = NormalizeLot(lotSize * 0.5);
        double lotSize2 = NormalizeLot(lotSize * 0.5);

        if(trade.Sell(lotSize1, _Symbol, price, sl, tp1, "RSI-EMA SELL TP1"))
        {
            Print("SELL order 1 executed at ", price, " SL: ", sl, " TP1: ", tp1);

            if(trade.Sell(lotSize2, _Symbol, price, sl, tp2, "RSI-EMA SELL TP2"))
            {
                Print("SELL order 2 executed at ", price, " SL: ", sl, " TP2: ", tp2);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate stop loss based on ATR                                 |
//+------------------------------------------------------------------+
double CalculateStopLoss(double entryPrice, double atrValue, bool isBuy)
{
    double slDistance = ATR_SL_Multiplier * atrValue;

    // Convert to pips for limit checking
    double point = symbol.Point();
    double pipValue = (symbol.Digits() == 5 || symbol.Digits() == 3) ? point * 10 : point;
    double slPips = slDistance / pipValue;

    // Apply min/max limits
    if(slPips < MinStopLoss)
        slDistance = MinStopLoss * pipValue;
    else if(slPips > MaxStopLoss)
        slDistance = MaxStopLoss * pipValue;

    if(isBuy)
        return entryPrice - slDistance;
    else
        return entryPrice + slDistance;
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk                                 |
//+------------------------------------------------------------------+
double CalculateLotSize(double atrValue)
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * RiskPercent / 100.0;

    // Calculate stop loss distance
    double slDistance = ATR_SL_Multiplier * atrValue;
    double point = symbol.Point();
    double pipValue = (symbol.Digits() == 5 || symbol.Digits() == 3) ? point * 10 : point;
    double slPips = slDistance / pipValue;

    // Apply limits
    if(slPips < MinStopLoss) slPips = MinStopLoss;
    if(slPips > MaxStopLoss) slPips = MaxStopLoss;

    double tickValue = symbol.TickValue();
    double lotSize = riskAmount / (slPips * pipValue / point * tickValue);

    return NormalizeLot(lotSize);
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                     |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    if(!UseTimeFilter)
        return true;

    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);

    int currentHour = timeStruct.hour;
    int currentDay = timeStruct.day_of_week;

    // Avoid Friday after specified hour
    if(AvoidFriday && currentDay == 5 && currentHour >= FridayCloseHour)
        return false;

    // Check if within London or New York session
    bool inLondonSession = (currentHour >= LondonStart && currentHour < LondonEnd);
    bool inNewYorkSession = (currentHour >= NewYorkStart && currentHour < NewYorkEnd);

    return (inLondonSession || inNewYorkSession);
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                     |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    if(!UseTimeFilter)
        return true;

    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);

    int currentHour = timeStruct.hour;

    if(StartHour <= EndHour)
        return (currentHour >= StartHour && currentHour < EndHour);
    else
        return (currentHour >= StartHour || currentHour < EndHour);
}

//+------------------------------------------------------------------+
//| Check for new trading session                                   |
//+------------------------------------------------------------------+
void CheckNewSession()
{
    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);

    datetime currentSessionStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE) + " " +
                                               IntegerToString(StartHour) + ":00:00");

    if(sessionStartTime != currentSessionStart)
    {
        sessionStartTime = currentSessionStart;
        tradesInSession = 0;
        Print("New trading session started. Trade counter reset.");
    }
}

//+------------------------------------------------------------------+
//| Check for bullish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBullishEngulfing()
{
    double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
    double open0 = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double close0 = iClose(_Symbol, PERIOD_CURRENT, 0);

    // Previous candle bearish, current candle bullish and engulfs previous
    return (close1 < open1 && close0 > open0 &&
            open0 < close1 && close0 > open1);
}

//+------------------------------------------------------------------+
//| Check for bearish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBearishEngulfing()
{
    double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
    double open0 = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double close0 = iClose(_Symbol, PERIOD_CURRENT, 0);

    // Previous candle bullish, current candle bearish and engulfs previous
    return (close1 > open1 && close0 < open0 &&
            open0 > close1 && close0 < open1);
}

//+------------------------------------------------------------------+
//| Check for bullish rejection (hammer/doji with long lower wick)  |
//+------------------------------------------------------------------+
bool IsBullishRejection()
{
    double open = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double close = iClose(_Symbol, PERIOD_CURRENT, 0);
    double high = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double low = iLow(_Symbol, PERIOD_CURRENT, 0);

    double bodySize = MathAbs(close - open);
    double lowerWick = MathMin(open, close) - low;
    double upperWick = high - MathMax(open, close);
    double totalRange = high - low;

    // Long lower wick with small body
    return (lowerWick > bodySize * 2 &&
            bodySize / totalRange < (1.0 - CandleBodyRatio) &&
            lowerWick > upperWick);
}

//+------------------------------------------------------------------+
//| Check for bearish rejection (shooting star with long upper wick)|
//+------------------------------------------------------------------+
bool IsBearishRejection()
{
    double open = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double close = iClose(_Symbol, PERIOD_CURRENT, 0);
    double high = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double low = iLow(_Symbol, PERIOD_CURRENT, 0);

    double bodySize = MathAbs(close - open);
    double lowerWick = MathMin(open, close) - low;
    double upperWick = high - MathMax(open, close);
    double totalRange = high - low;

    // Long upper wick with small body
    return (upperWick > bodySize * 2 &&
            bodySize / totalRange < (1.0 - CandleBodyRatio) &&
            upperWick > lowerWick);
}

//+------------------------------------------------------------------+
//| Check if price is near fractal trap zone                        |
//+------------------------------------------------------------------+
bool IsNearFractalTrap()
{
    // Get recent fractal levels
    double upperFractal = GetRecentFractal(true);
    double lowerFractal = GetRecentFractal(false);
    double currentPrice = symbol.Bid();

    // Get current ATR for distance calculation
    double atr[];
    ArraySetAsSeries(atr, true);
    if(CopyBuffer(handleATR, 0, 0, 1, atr) < 1)
        return false;

    double fractalDistance = atr[0] * 0.5; // Half ATR distance

    // Check if price is too close to recent fractal levels
    if((upperFractal > 0 && MathAbs(currentPrice - upperFractal) < fractalDistance) ||
       (lowerFractal > 0 && MathAbs(currentPrice - lowerFractal) < fractalDistance))
    {
        return true; // Near fractal trap
    }

    return false;
}

//+------------------------------------------------------------------+
//| Get recent fractal level                                         |
//+------------------------------------------------------------------+
double GetRecentFractal(bool isUpper)
{
    for(int i = FractalPeriod; i < 50; i++) // Look back 50 bars max
    {
        if(isUpper)
        {
            // Check for upper fractal
            bool isUpperFractal = true;
            double centerHigh = iHigh(_Symbol, PERIOD_CURRENT, i);

            for(int j = 1; j <= FractalPeriod; j++)
            {
                if(iHigh(_Symbol, PERIOD_CURRENT, i-j) >= centerHigh ||
                   iHigh(_Symbol, PERIOD_CURRENT, i+j) >= centerHigh)
                {
                    isUpperFractal = false;
                    break;
                }
            }

            if(isUpperFractal)
                return centerHigh;
        }
        else
        {
            // Check for lower fractal
            bool isLowerFractal = true;
            double centerLow = iLow(_Symbol, PERIOD_CURRENT, i);

            for(int j = 1; j <= FractalPeriod; j++)
            {
                if(iLow(_Symbol, PERIOD_CURRENT, i-j) <= centerLow ||
                   iLow(_Symbol, PERIOD_CURRENT, i+j) <= centerLow)
                {
                    isLowerFractal = false;
                    break;
                }
            }

            if(isLowerFractal)
                return centerLow;
        }
    }

    return 0; // No fractal found
}

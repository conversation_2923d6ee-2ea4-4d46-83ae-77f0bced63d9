//+------------------------------------------------------------------+
//|                                                   S.G_XAUUSD.mq5 |
//|                                    Adaptive Trading Robot for Gold |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "S.G Trading Robot"
#property link      ""
#property version   "1.00"
#property description "Adaptive Trading Robot with 60%+ Win Rate Target"

//--- Include necessary files
#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include <Trade/SymbolInfo.mqh>

//--- Input parameters
input group "=== STRATEGY SETTINGS ==="
input double   RiskPercent = 1.0;           // Risk per trade (%)
input double   ATR_Multiplier_SL = 1.5;     // ATR multiplier for Stop Loss
input double   ATR_Multiplier_TP = 2.5;     // ATR multiplier for Take Profit
input double   MinRR_Ratio = 1.5;           // Minimum Risk:Reward ratio
input int      MaxTradesPerSession = 2;     // Maximum trades per session

input group "=== INDICATOR SETTINGS ==="
input int      EMA_Fast_Period = 50;        // EMA Fast period
input int      EMA_Slow_Period = 200;       // EMA Slow period
input int      ATR_Period = 14;             // ATR period
input int      StochRSI_Period = 14;        // Stochastic RSI period
input int      StochRSI_K = 3;              // Stochastic RSI %K
input int      StochRSI_D = 3;              // Stochastic RSI %D
input double   ATR_Volatility_Threshold = 0.0001; // Minimum ATR for trading

input group "=== TIME FILTER ==="
input bool     UseTimeFilter = true;        // Enable time filter
input int      StartHour = 8;               // Trading start hour (server time)
input int      EndHour = 17;                // Trading end hour (server time)

input group "=== FRACTAL FILTER ==="
input bool     UseFractalFilter = true;     // Enable fractal manipulation filter
input int      FractalPeriod = 5;           // Fractal period
input double   CandleBodyRatio = 0.6;       // Minimum candle body ratio

//--- Global variables
int handleEMA_Fast, handleEMA_Slow, handleATR, handleStochRSI;
int tradesInSession = 0;
datetime lastTradeTime = 0;
datetime sessionStartTime = 0;

//--- Trade management
CTrade trade;
CPositionInfo position;
CSymbolInfo symbol;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize symbol
    if(!symbol.Name(_Symbol))
    {
        Print("Error initializing symbol: ", _Symbol);
        return INIT_FAILED;
    }
    
    // Initialize indicators
    handleEMA_Fast = iMA(_Symbol, PERIOD_CURRENT, EMA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
    handleEMA_Slow = iMA(_Symbol, PERIOD_CURRENT, EMA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);
    handleATR = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
    handleStochRSI = iStochastic(_Symbol, PERIOD_CURRENT, StochRSI_Period, StochRSI_K, StochRSI_D, MODE_SMA, STO_CLOSECLOSE);
    
    // Check if all indicators are created successfully
    if(handleEMA_Fast == INVALID_HANDLE || handleEMA_Slow == INVALID_HANDLE || 
       handleATR == INVALID_HANDLE || handleStochRSI == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }
    
    Print("S.G_XAUUSD Expert Advisor initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(handleEMA_Fast);
    IndicatorRelease(handleEMA_Slow);
    IndicatorRelease(handleATR);
    IndicatorRelease(handleStochRSI);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime == lastBarTime)
        return;
    
    lastBarTime = currentBarTime;
    
    // Reset session trades counter at session start
    CheckNewSession();
    
    // Check trading conditions
    if(!IsTimeToTrade())
        return;
        
    if(tradesInSession >= MaxTradesPerSession)
        return;
        
    // Get current market data
    double currentPrice = symbol.Bid();
    
    // Get indicator values
    double emaFast[], emaSlow[], atr[], stochMain[], stochSignal[];
    
    if(!GetIndicatorValues(emaFast, emaSlow, atr, stochMain, stochSignal))
        return;
    
    // Check volatility threshold
    if(atr[0] < ATR_Volatility_Threshold)
        return;
    
    // Check for existing positions
    if(position.Select(_Symbol))
        return;
    
    // Analyze market conditions
    int signal = AnalyzeMarket(emaFast, emaSlow, stochMain, stochSignal, atr);
    
    if(signal != 0)
    {
        ExecuteTrade(signal, atr[0]);
    }
}

//+------------------------------------------------------------------+
//| Get indicator values                                             |
//+------------------------------------------------------------------+
bool GetIndicatorValues(double &emaFast[], double &emaSlow[], double &atr[], 
                       double &stochMain[], double &stochSignal[])
{
    ArraySetAsSeries(emaFast, true);
    ArraySetAsSeries(emaSlow, true);
    ArraySetAsSeries(atr, true);
    ArraySetAsSeries(stochMain, true);
    ArraySetAsSeries(stochSignal, true);
    
    if(CopyBuffer(handleEMA_Fast, 0, 0, 3, emaFast) < 3 ||
       CopyBuffer(handleEMA_Slow, 0, 0, 3, emaSlow) < 3 ||
       CopyBuffer(handleATR, 0, 0, 3, atr) < 3 ||
       CopyBuffer(handleStochRSI, 0, 0, 0, 3, stochMain) < 3 ||
       CopyBuffer(handleStochRSI, 1, 0, 3, stochSignal) < 3)
    {
        Print("Error copying indicator buffers");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Analyze market conditions                                        |
//+------------------------------------------------------------------+
int AnalyzeMarket(const double &emaFast[], const double &emaSlow[], 
                 const double &stochMain[], const double &stochSignal[], 
                 const double &atr[])
{
    // Trend analysis
    bool bullishTrend = emaFast[0] > emaSlow[0];
    bool bearishTrend = emaFast[0] < emaSlow[0];
    
    // Stochastic RSI analysis
    bool stochOversold = stochMain[1] < 20 && stochMain[0] > stochMain[1];
    bool stochOverbought = stochMain[1] > 80 && stochMain[0] < stochMain[1];
    
    // Candle pattern analysis
    bool bullishCandle = IsBullishEngulfing() || IsBullishRejection();
    bool bearishCandle = IsBearishEngulfing() || IsBearishRejection();
    
    // Fractal filter
    bool fractalFilterPassed = true;
    if(UseFractalFilter)
        fractalFilterPassed = !IsNearFractalTrap();
    
    // BUY signal
    if(bullishTrend && stochOversold && bullishCandle && fractalFilterPassed)
        return 1;
    
    // SELL signal
    if(bearishTrend && stochOverbought && bearishCandle && fractalFilterPassed)
        return -1;
    
    return 0;
}

//+------------------------------------------------------------------+
//| Execute trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(int signal, double currentATR)
{
    double lotSize = CalculateLotSize(currentATR);
    double sl, tp;
    
    if(signal == 1) // BUY
    {
        double price = symbol.Ask();
        sl = price - (ATR_Multiplier_SL * currentATR);
        tp = price + (ATR_Multiplier_TP * currentATR);
        
        // Ensure minimum RR ratio
        double risk = price - sl;
        double reward = tp - price;
        if(reward / risk < MinRR_Ratio)
            tp = price + (risk * MinRR_Ratio);
        
        if(trade.Buy(lotSize, _Symbol, price, sl, tp, "S.G_XAUUSD BUY"))
        {
            tradesInSession++;
            lastTradeTime = TimeCurrent();
            Print("BUY order executed at ", price, " SL: ", sl, " TP: ", tp);
        }
    }
    else if(signal == -1) // SELL
    {
        double price = symbol.Bid();
        sl = price + (ATR_Multiplier_SL * currentATR);
        tp = price - (ATR_Multiplier_TP * currentATR);
        
        // Ensure minimum RR ratio
        double risk = sl - price;
        double reward = price - tp;
        if(reward / risk < MinRR_Ratio)
            tp = price - (risk * MinRR_Ratio);
        
        if(trade.Sell(lotSize, _Symbol, price, sl, tp, "S.G_XAUUSD SELL"))
        {
            tradesInSession++;
            lastTradeTime = TimeCurrent();
            Print("SELL order executed at ", price, " SL: ", sl, " TP: ", tp);
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk                                 |
//+------------------------------------------------------------------+
double CalculateLotSize(double atrValue)
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * RiskPercent / 100.0;
    double stopLossPoints = ATR_Multiplier_SL * atrValue;
    double tickValue = symbol.TickValue();
    double tickSize = symbol.TickSize();
    
    double lotSize = riskAmount / (stopLossPoints / tickSize * tickValue);
    
    // Normalize lot size
    double minLot = symbol.LotsMin();
    double maxLot = symbol.LotsMax();
    double lotStep = symbol.LotsStep();
    
    lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                     |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    if(!UseTimeFilter)
        return true;

    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);

    int currentHour = timeStruct.hour;

    if(StartHour <= EndHour)
        return (currentHour >= StartHour && currentHour < EndHour);
    else
        return (currentHour >= StartHour || currentHour < EndHour);
}

//+------------------------------------------------------------------+
//| Check for new trading session                                   |
//+------------------------------------------------------------------+
void CheckNewSession()
{
    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);

    datetime currentSessionStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE) + " " +
                                               IntegerToString(StartHour) + ":00:00");

    if(sessionStartTime != currentSessionStart)
    {
        sessionStartTime = currentSessionStart;
        tradesInSession = 0;
        Print("New trading session started. Trade counter reset.");
    }
}

//+------------------------------------------------------------------+
//| Check for bullish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBullishEngulfing()
{
    double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
    double open0 = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double close0 = iClose(_Symbol, PERIOD_CURRENT, 0);

    // Previous candle bearish, current candle bullish and engulfs previous
    return (close1 < open1 && close0 > open0 &&
            open0 < close1 && close0 > open1);
}

//+------------------------------------------------------------------+
//| Check for bearish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBearishEngulfing()
{
    double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
    double open0 = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double close0 = iClose(_Symbol, PERIOD_CURRENT, 0);

    // Previous candle bullish, current candle bearish and engulfs previous
    return (close1 > open1 && close0 < open0 &&
            open0 > close1 && close0 < open1);
}

//+------------------------------------------------------------------+
//| Check for bullish rejection (hammer/doji with long lower wick)  |
//+------------------------------------------------------------------+
bool IsBullishRejection()
{
    double open = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double close = iClose(_Symbol, PERIOD_CURRENT, 0);
    double high = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double low = iLow(_Symbol, PERIOD_CURRENT, 0);

    double bodySize = MathAbs(close - open);
    double lowerWick = MathMin(open, close) - low;
    double upperWick = high - MathMax(open, close);
    double totalRange = high - low;

    // Long lower wick with small body
    return (lowerWick > bodySize * 2 &&
            bodySize / totalRange < (1.0 - CandleBodyRatio) &&
            lowerWick > upperWick);
}

//+------------------------------------------------------------------+
//| Check for bearish rejection (shooting star with long upper wick)|
//+------------------------------------------------------------------+
bool IsBearishRejection()
{
    double open = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double close = iClose(_Symbol, PERIOD_CURRENT, 0);
    double high = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double low = iLow(_Symbol, PERIOD_CURRENT, 0);

    double bodySize = MathAbs(close - open);
    double lowerWick = MathMin(open, close) - low;
    double upperWick = high - MathMax(open, close);
    double totalRange = high - low;

    // Long upper wick with small body
    return (upperWick > bodySize * 2 &&
            bodySize / totalRange < (1.0 - CandleBodyRatio) &&
            upperWick > lowerWick);
}

//+------------------------------------------------------------------+
//| Check if price is near fractal trap zone                        |
//+------------------------------------------------------------------+
bool IsNearFractalTrap()
{
    // Get recent fractal levels
    double upperFractal = GetRecentFractal(true);
    double lowerFractal = GetRecentFractal(false);
    double currentPrice = symbol.Bid();

    // Get current ATR for distance calculation
    double atr[];
    ArraySetAsSeries(atr, true);
    if(CopyBuffer(handleATR, 0, 0, 1, atr) < 1)
        return false;

    double fractalDistance = atr[0] * 0.5; // Half ATR distance

    // Check if price is too close to recent fractal levels
    if((upperFractal > 0 && MathAbs(currentPrice - upperFractal) < fractalDistance) ||
       (lowerFractal > 0 && MathAbs(currentPrice - lowerFractal) < fractalDistance))
    {
        return true; // Near fractal trap
    }

    return false;
}

//+------------------------------------------------------------------+
//| Get recent fractal level                                         |
//+------------------------------------------------------------------+
double GetRecentFractal(bool isUpper)
{
    for(int i = FractalPeriod; i < 50; i++) // Look back 50 bars max
    {
        if(isUpper)
        {
            // Check for upper fractal
            bool isUpperFractal = true;
            double centerHigh = iHigh(_Symbol, PERIOD_CURRENT, i);

            for(int j = 1; j <= FractalPeriod; j++)
            {
                if(iHigh(_Symbol, PERIOD_CURRENT, i-j) >= centerHigh ||
                   iHigh(_Symbol, PERIOD_CURRENT, i+j) >= centerHigh)
                {
                    isUpperFractal = false;
                    break;
                }
            }

            if(isUpperFractal)
                return centerHigh;
        }
        else
        {
            // Check for lower fractal
            bool isLowerFractal = true;
            double centerLow = iLow(_Symbol, PERIOD_CURRENT, i);

            for(int j = 1; j <= FractalPeriod; j++)
            {
                if(iLow(_Symbol, PERIOD_CURRENT, i-j) <= centerLow ||
                   iLow(_Symbol, PERIOD_CURRENT, i+j) <= centerLow)
                {
                    isLowerFractal = false;
                    break;
                }
            }

            if(isLowerFractal)
                return centerLow;
        }
    }

    return 0; // No fractal found
}

Awesome. Let’s build a **robust, adaptive trading robot** in **MVP (Minimum Viable Product)** format — designed to **adjust to market conditions**, **handle volatility and manipulations**, and aim for a **win rate of 60%+**.

---

### 🔧 **📌 MVP STRUCTURE — ADAPTIVE TRADING ROBOT**

---

### 🧠 1. **Strategy Objective (Core Idea)**

> Build a smart EA that dynamically **adapts to volatility**, avoids fakeouts/manipulation, and only trades when conditions favor high-probability setups.

✅ Goal: 60%+ win rate, no martingale, no grid.

---

### ⚙️ 2. **Core Indicators (Adaptive and Confirming Tools)**

| Purpose                | Tool                                      | How It's Used                                                                                |
| ---------------------- | ----------------------------------------- | -------------------------------------------------------------------------------------------- |
| 🔄 Volatility Filter   | **ATR (14)**                              | Measures volatility. Expands SL/TP when ATR is high, tightens when low.                      |
| 🎯 Trend Direction     | **EMA 50 + EMA 200**                      | Confirms trend. Long if EMA50 > EMA200, Short if opposite.                                   |
| 🔍 Entry Precision     | **Stochastic RSI (14, 3, 3)**             | Entry when oversold/buy or overbought/sell **in direction of trend**.                        |
| 🪤 Manipulation Filter | **Fractal Breakouts + Candle Body Ratio** | Avoids false breakouts & stop hunts by filtering through smart fractals and candle strength. |

---

### 📈 3. **Entry Rules (BUY)**

* EMA 50 > EMA 200 ✅ (trend up)
* ATR > threshold (adjustable) ✅ (enough volatility)
* Stoch RSI crosses **below 20 then back up** ✅ (oversold reversal)
* Bullish engulfing candle or long wick rejection at a support zone ✅
* No recent high-impact news in next 30 min (optional with news filter)

> **SELL** rules = reversed conditions.

---

### 🛡️ 4. **Risk & Money Management**

| Setting      | Details                                                       |
| ------------ | ------------------------------------------------------------- |
| SL/TP        | Dynamic based on ATR. e.g. SL = 1.5 \* ATR, TP = 2.5 \* ATR   |
| RR Ratio     | 1:1.5 minimum (targeting positive expectancy)                 |
| Trade Volume | 1% risk per trade or fixed lot                                |
| Max Trades   | Max 2 trades per session, avoid overtrading                   |
| Trading Time | Only trades during high-liquidity hours (London + NY overlap) |

---

### ⚙️ 5. **Market Adaptation Logic**

* **ATR Tracker:** Every candle, the robot recalculates SL/TP size based on real-time ATR — adapts to spikes (like NFP).
* **Session Awareness:** Avoids entries in low-volume Asian session unless volatility spikes.
* **Fractal Trap Detection:** Uses recent fractal patterns + candle size ratios to spot manipulation zones (false breakouts).
* **News Avoidance (Optional):** Disables trading X minutes before and after red folder news.

---

### 🧪 6. **Features for MVP Build**

| Feature                       | Included?                                    |
| ----------------------------- | -------------------------------------------- |
| ✅ EMA + ATR Logic             | Yes                                          |
| ✅ Stoch RSI Entry             | Yes                                          |
| ✅ Dynamic SL/TP based on ATR  | Yes                                          |
| ✅ Fractal filter for fakeouts | Yes                                          |
| ✅ Volatility threshold        | Yes                                          |
| 🟡 News filter                | Optional (based on economic calendar plugin) |
| ✅ Trade logging               | Yes (CSV / journal)                          |

---

### 📊 7. **Target Market Pairs / Conditions**

* Best on: **XAU/USD**, **GBP/USD**, **US30**
* Timeframe: **M5 or M15**
* Avoid: Asian session (unless using auto volatility tracker)

---

### 🤖 8. **Platform**

* **MT5 Expert Advisor**
* Written in **MQL5**
* Clean modular design for easy optimization and testing

---

### 🔚 9. **Success Metrics**

| Metric         | Target |
| -------------- | ------ |
| Win Rate       | ≥ 60%  |
| Monthly Return | 5–15%  |
| Max Drawdown   | < 15%  |
| Profit Factor  | > 1.5  |

---

Would you like me to now generate the **code structure** in MQL5?
Or first, would you like a **step-by-step dev plan** for building and testing it?

We can also name this robot now. Got any ideas?
